<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="DMA直接内存访问连接" id="dma-connection">
    <mxGraphModel dx="3108" dy="3952" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="2400" pageHeight="1800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1800" y="860" width="350" height="150" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CONTROLLER" value="DMA Controller Direct Memory Access" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="400" height="150" as="geometry" />
        </mxCell>
        <mxCell id="SYSTEM_MEMORY" value="System Memory RAM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="680" y="830" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="PERIPHERAL_MEMORY" value="Peripheral Memory Registers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="430" y="1010" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="BUFFER_MEMORY" value="Buffer Memory Data Buffers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="880" y="1040" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="FRAM_MEMORY" value="FRAM Memory Non-volatile Storage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="100" y="830" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CH0" value="DMA Channel 0 SPI1 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1275" y="-100" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CH1" value="DMA Channel 1 SPI2 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1540" y="-260" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CH2" value="DMA Channel 2 SPI3 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1020" y="-260" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CH3" value="DMA Channel 3 SPI4 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="790" y="-100" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CH4" value="DMA Channel 4 I2C1 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1770" y="205" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="DMA_CH5" value="DMA Channel 5 ADC1 Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="2000" y="385" width="250" height="90" as="geometry" />
        </mxCell>
        <mxCell id="SPI_INTERFACES" value="SPI Interfaces SPI1 SPI2 SPI3 SPI4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="80" y="10" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="I2C_INTERFACES" value="I2C Interfaces I2C1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="80" y="190" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="ADC_INTERFACES" value="ADC Interfaces ADC1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="80" y="370" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="CAN_INTERFACES" value="CAN Interfaces CAN1 CAN2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="80" y="550" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_DMA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="MCU" target="DMA_CONTROLLER" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1400" y="596" as="sourcePoint" />
            <mxPoint x="1665" y="-174" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1400" y="196" />
              <mxPoint x="1665" y="196" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeDMA_CH2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1400" y="596" as="sourcePoint" />
            <mxPoint x="1145" y="-174" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1400" y="196" />
              <mxPoint x="1145" y="196" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeDMA_CH3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" target="DMA_CH3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1400" y="605" as="sourcePoint" />
            <mxPoint x="995" y="-5" as="targetPoint" />
            <Array as="points">
              <mxPoint x="1400" y="195" />
              <mxPoint x="915" y="195" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeDMA_CH4" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1895" y="630" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeDMA_CH5" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2125" y="710" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeDMA_SYS_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="SYSTEM_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_PERI_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="PERIPHERAL_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_BUFFER_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="BUFFER_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_FRAM_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="FRAM_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C_DMA_CH4" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="I2C_INTERFACES" target="DMA_CH4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeADC_DMA_CH5" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="ADC_INTERFACES" target="DMA_CH5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_DMA" value="DMA 控制器 高效数据传输" style="text;html=1;strokeColor=none;fillColor=#e1bee7;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="1430" y="800" width="190" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
