<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="DMA直接内存访问连接" id="dma-connection">
    <mxGraphModel dx="2000" dy="1600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- MCU 主控制器 -->
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- DMA 控制器 -->
        <mxCell id="DMA_CONTROLLER" value="DMA Controller Direct Memory Access" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="400" width="300" height="100" as="geometry" />
        </mxCell>
        
        <!-- 内存区域 -->
        <mxCell id="SYSTEM_MEMORY" value="System Memory RAM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="200" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="PERIPHERAL_MEMORY" value="Peripheral Memory Registers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="320" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="BUFFER_MEMORY" value="Buffer Memory Data Buffers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="440" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="FRAM_MEMORY" value="FRAM Memory Non-volatile Storage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="560" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- DMA 通道 -->
        <mxCell id="DMA_CH0" value="DMA Channel 0 SPI1 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="DMA_CH1" value="DMA Channel 1 SPI2 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="DMA_CH2" value="DMA Channel 2 SPI3 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1200" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="DMA_CH3" value="DMA Channel 3 SPI4 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="250" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="DMA_CH4" value="DMA Channel 4 I2C1 Tx Rx" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="250" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="DMA_CH5" value="DMA Channel 5 ADC1 Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1200" y="250" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 外设接口 -->
        <mxCell id="SPI_INTERFACES" value="SPI Interfaces SPI1 SPI2 SPI3 SPI4" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="150" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="I2C_INTERFACES" value="I2C Interfaces I2C1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="280" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="ADC_INTERFACES" value="ADC Interfaces ADC1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="410" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="CAN_INTERFACES" value="CAN Interfaces CAN1 CAN2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="540" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- MCU 到 DMA 的连接 -->
        <mxCell id="edgeMCU_DMA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="MCU" target="DMA_CONTROLLER" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- DMA 到通道的连接 -->
        <mxCell id="edgeDMA_CH0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH4" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_CH5" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="DMA_CH5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- DMA 到内存的连接 -->
        <mxCell id="edgeDMA_SYS_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="SYSTEM_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_PERI_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="PERIPHERAL_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_BUFFER_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="BUFFER_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_FRAM_MEM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="DMA_CONTROLLER" target="FRAM_MEMORY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 外设到 DMA 通道的连接 -->
        <mxCell id="edgeSPI_DMA_CH0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_DMA_CH3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=2;" parent="1" source="SPI_INTERFACES" target="DMA_CH3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C_DMA_CH4" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="I2C_INTERFACES" target="DMA_CH4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeADC_DMA_CH5" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="ADC_INTERFACES" target="DMA_CH5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 标签说明 -->
        <mxCell id="LABEL_DMA" value="DMA 控制器 高效数据传输" style="text;html=1;strokeColor=none;fillColor=#e1bee7;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="800" y="350" width="200" height="50" as="geometry" />
        </mxCell>
        
        <!-- DMA 功能说明 -->
        <mxCell id="LABEL_DMA_FUNCTIONS" value="DMA 功能: 无需 CPU 干预的数据传输 减少 CPU 负载 提高系统效率 支持多通道并发" style="text;html=1;strokeColor=none;fillColor=#e1bee7;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- 传输模式说明 -->
        <mxCell id="LABEL_TRANSFER_MODES" value="传输模式: Memory to Memory Memory to Peripheral Peripheral to Memory Circular Buffer Mode" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="750" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- 优先级说明 -->
        <mxCell id="LABEL_PRIORITY" value="优先级管理: Very High Priority High Priority Medium Priority Low Priority Round-robin 调度" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="600" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 应用场景说明 -->
        <mxCell id="LABEL_APPLICATIONS" value="应用场景: AFE 数据采集 ADC 连续转换 SPI 高速通信 I2C 批量传输 FRAM 数据存储" style="text;html=1;strokeColor=none;fillColor=#e3f2fd;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="750" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 性能参数说明 -->
        <mxCell id="LABEL_PERFORMANCE" value="性能参数: 传输速度 高达 100 MB/s 通道数量 16 个独立通道 数据宽度 8/16/32 bit 突发传输 支持" style="text;html=1;strokeColor=none;fillColor=#fffde7;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="900" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 中断管理说明 -->
        <mxCell id="LABEL_INTERRUPTS" value="中断管理: Transfer Complete Half Transfer Error Interrupt FIFO Threshold 自动重载模式" style="text;html=1;strokeColor=none;fillColor=#e1bee7;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="900" width="250" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
