<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="CAN接口连接" id="can-interface-connection">
    <mxGraphModel dx="1793" dy="1076" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="CAN1" value="CAN1 canREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="250" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CAN2" value="CAN2 canREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="450" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="HET2" value="HET2 hetREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="650" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PEX_CAN" value="PEX PCA9539" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="850" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CAN1_TRANSCEIVER" value="CAN1 Transceiver TJA1050 SN65HVD230" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="150" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CAN2_TRANSCEIVER" value="CAN2 Transceiver TJA1050 SN65HVD230" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="350" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CAN1_ENABLE" value="CAN1 Enable HET2 Pin18" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="210" y="625" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CAN1_STANDBY" value="CAN1 Standby HET2 Pin23" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="210" y="695" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CAN2_ENABLE" value="CAN2 Enable PEX Port0 Pin2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="450" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CAN2_STANDBY" value="CAN2 Standby PEX Port0 Pin3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="520" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="IMD_DEVICE" value="IMD Device Bender ISO165C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="100" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CURRENT_SENSOR" value="Current Sensor CAN Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="200" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="EXTERNAL_BMS" value="External BMS Master Slave" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="300" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="VEHICLE_ECU" value="Vehicle ECU Engine Control" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="400" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CHARGER" value="Charger DC AC Charger" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="500" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_CAN1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" parent="1" source="MCU" target="CAN1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_CAN2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" parent="1" source="MCU" target="CAN2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_HET2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="HET2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_PEX" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" parent="1" source="MCU" target="PEX_CAN" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN1_TRANSCEIVER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" parent="1" source="CAN1" target="CAN1_TRANSCEIVER" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN2_TRANSCEIVER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" parent="1" source="CAN2" target="CAN2_TRANSCEIVER" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET2_CAN1_ENABLE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" parent="1" source="HET2" target="CAN1_ENABLE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET2_CAN1_STANDBY" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" parent="1" source="HET2" target="CAN1_STANDBY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX_CAN2_ENABLE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PEX_CAN" target="CAN2_ENABLE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX_CAN2_STANDBY" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PEX_CAN" target="CAN2_STANDBY" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN1_IMD" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="CAN1_TRANSCEIVER" target="IMD_DEVICE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN1_CURRENT" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="CAN1_TRANSCEIVER" target="CURRENT_SENSOR" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN1_BMS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="CAN1_TRANSCEIVER" target="EXTERNAL_BMS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN2_VEHICLE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="CAN2_TRANSCEIVER" target="VEHICLE_ECU" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN2_CHARGER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="CAN2_TRANSCEIVER" target="CHARGER" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CAN1" value="CAN1 500 kbps 主要通信总线" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="150" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CAN2" value="CAN2 500 kbps 隔离通信总线" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="550" width="200" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CAN1_FUNCTION" value="CAN1 功能: IMD 通信 电流传感器 调试信息 故障报告" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CAN2_FUNCTION" value="CAN2 功能: 车辆通信 充电器通信 外部 BMS 隔离保护" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="720" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_MESSAGE_TYPES" value="CAN 消息类型: 电池状态 电压温度 故障诊断 控制命令 充电状态" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="840" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CONTROL_PINS" value="控制引脚: Enable 收发器使能 Standby 待机模式 CAN1 HET2控制 CAN2 PEX控制" style="text;html=1;strokeColor=none;fillColor=#fce4ec;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="960" width="250" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
