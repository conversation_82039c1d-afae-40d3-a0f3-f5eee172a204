<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="GPIO和PWM连接" id="gpio-pwm-connection">
    <mxGraphModel dx="2000" dy="1600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- MCU 主控制器 -->
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- GPIO 接口 -->
        <mxCell id="GIOA" value="GIOA gioREG" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="GIOB" value="GIOB gioREG" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="HET1" value="HET1 hetREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="360" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="HET2" value="HET2 hetREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- PWM 接口 -->
        <mxCell id="PWM_ETPWM" value="PWM etpwmREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="520" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- GPIO 输出设备 -->
        <mxCell id="LED_STATUS" value="Status LED GIOA Pin0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="LED_ERROR" value="Error LED GIOA Pin1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="700" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="RELAY_MAIN" value="Main Relay GIOB Pin0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="250" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="RELAY_PRECHARGE" value="Precharge Relay GIOB Pin1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="700" y="250" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- HET GPIO 设备 -->
        <mxCell id="INTERLOCK_OUT" value="Interlock Output HET1 Pin29" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="350" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="MAXIM_ENABLE" value="Maxim Enable HET1 Pin21" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="700" y="350" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="SPS_CONTROL" value="SPS Control HET2 Pin16 9 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="450" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="CAN_CONTROL" value="CAN Control HET2 Pin18 23" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="700" y="450" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- GPIO 输入设备 -->
        <mxCell id="INTERLOCK_IN" value="Interlock Input HET1 Pin30" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="350" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="EMERGENCY_STOP" value="Emergency Stop GIOA Pin7" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- PWM 控制设备 -->
        <mxCell id="FAN_CONTROL" value="Cooling Fan PWM Channel 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="550" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="BUZZER_CONTROL" value="Buzzer PWM Channel 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="700" y="550" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="HEATER_CONTROL" value="Battery Heater PWM Channel 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="650" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- MCU 到接口的连接 -->
        <mxCell id="edgeMCU_GIOA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="GIOA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_GIOB" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="GIOB" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_HET1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="HET1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_HET2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="HET2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_PWM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="MCU" target="PWM_ETPWM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- GPIO 输出连接 -->
        <mxCell id="edgeGIOA_LED_STATUS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="GIOA" target="LED_STATUS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeGIOA_LED_ERROR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" parent="1" source="GIOA" target="LED_ERROR" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeGIOB_RELAY_MAIN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="GIOB" target="RELAY_MAIN" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeGIOB_RELAY_PRECHARGE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="GIOB" target="RELAY_PRECHARGE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- HET 连接 -->
        <mxCell id="edgeHET1_INTERLOCK_OUT" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="HET1" target="INTERLOCK_OUT" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET1_MAXIM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="HET1" target="MAXIM_ENABLE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET2_SPS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="HET2" target="SPS_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET2_CAN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="HET2" target="CAN_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- GPIO 输入连接 -->
        <mxCell id="edgeINTERLOCK_IN_HET1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="INTERLOCK_IN" target="HET1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeEMERGENCY_GIOA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" parent="1" source="EMERGENCY_STOP" target="GIOA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- PWM 连接 -->
        <mxCell id="edgePWM_FAN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PWM_ETPWM" target="FAN_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePWM_BUZZER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="PWM_ETPWM" target="BUZZER_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePWM_HEATER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="PWM_ETPWM" target="HEATER_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 标签说明 -->
        <mxCell id="LABEL_GPIO" value="GPIO 接口 数字输入输出" style="text;html=1;strokeColor=none;fillColor=#fce4ec;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="100" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="LABEL_PWM" value="PWM 接口 脉宽调制" style="text;html=1;strokeColor=none;fillColor=#f3e5f5;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="650" width="150" height="50" as="geometry" />
        </mxCell>
        
        <!-- GPIO 功能说明 -->
        <mxCell id="LABEL_GPIO_FUNCTIONS" value="GPIO 功能: 数字输入输出 中断触发 状态指示 设备控制 安全监控" style="text;html=1;strokeColor=none;fillColor=#fce4ec;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- PWM 功能说明 -->
        <mxCell id="LABEL_PWM_FUNCTIONS" value="PWM 功能: 速度控制 功率调节 温度控制 声音产生 调光控制" style="text;html=1;strokeColor=none;fillColor=#f3e5f5;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="750" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- HET 特性说明 -->
        <mxCell id="LABEL_HET_FEATURES" value="HET 特性: 高精度定时 独立处理器 复杂波形生成 事件捕获 实时响应" style="text;html=1;strokeColor=none;fillColor=#e3f2fd;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="900" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- 安全特性说明 -->
        <mxCell id="LABEL_SAFETY_FEATURES" value="安全特性: 紧急停止 互锁保护 故障检测 状态监控 冗余设计" style="text;html=1;strokeColor=none;fillColor=#ffebee;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="750" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- 应用场景说明 -->
        <mxCell id="LABEL_APPLICATIONS" value="应用场景: 状态指示 设备控制 安全保护 温度管理 用户交互" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="900" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- 控制参数说明 -->
        <mxCell id="LABEL_CONTROL_PARAMS" value="控制参数: PWM 频率 1kHz-100kHz 占空比 0-100% GPIO 电平 0V 5V 响应时间 <1ms 精度 0.1%" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="600" width="200" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
