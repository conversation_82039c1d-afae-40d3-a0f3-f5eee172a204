<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="MEAS测量管理系统" id="meas-management">
    <mxGraphModel dx="2000" dy="1600" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- MCU 主控制器 -->
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- MEAS 测量管理核心 -->
        <mxCell id="MEAS_CORE" value="MEAS Core Measurement Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="800" y="400" width="300" height="100" as="geometry" />
        </mxCell>
        
        <!-- 数据采集模块 -->
        <mxCell id="DATA_ACQUISITION" value="Data Acquisition 数据采集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="200" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="DATA_PROCESSING" value="Data Processing 数据处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="320" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="DATA_VALIDATION" value="Data Validation 数据验证" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="440" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="DATA_STORAGE" value="Data Storage 数据存储" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="500" y="560" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- 测量源 -->
        <mxCell id="AFE_SOURCE" value="AFE Analog Front End" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ADC_SOURCE" value="ADC Analog Digital Converter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="230" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="SENSOR_SOURCE" value="Sensors Temperature Pressure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="310" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="CAN_SOURCE" value="CAN Bus External Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="390" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="I2C_SOURCE" value="I2C Devices HTSensor RTC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="470" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 测量类型 -->
        <mxCell id="VOLTAGE_MEAS" value="Voltage Measurement 电压测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="CURRENT_MEAS" value="Current Measurement 电流测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="150" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="TEMP_MEAS" value="Temperature Measurement 温度测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="230" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="SOC_MEAS" value="SOC Measurement 电量测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="230" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="SOH_MEAS" value="SOH Measurement 健康度测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="310" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="POWER_MEAS" value="Power Measurement 功率测量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="310" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- 数据输出 -->
        <mxCell id="REAL_TIME_DATA" value="Real-time Data 实时数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="600" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="HISTORICAL_DATA" value="Historical Data 历史数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="600" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="ALARM_DATA" value="Alarm Data 报警数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="680" width="180" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="STATISTICS_DATA" value="Statistics Data 统计数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="1000" y="680" width="180" height="60" as="geometry" />
        </mxCell>
        
        <!-- MCU 到 MEAS 的连接 -->
        <mxCell id="edgeMCU_MEAS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="MCU" target="MEAS_CORE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- MEAS 到数据处理模块的连接 -->
        <mxCell id="edgeMEAS_DATA_ACQ" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="MEAS_CORE" target="DATA_ACQUISITION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_DATA_PROC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="MEAS_CORE" target="DATA_PROCESSING" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_DATA_VAL" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="MEAS_CORE" target="DATA_VALIDATION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_DATA_STOR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="MEAS_CORE" target="DATA_STORAGE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 测量源到数据采集的连接 -->
        <mxCell id="edgeAFE_DATA_ACQ" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="AFE_SOURCE" target="DATA_ACQUISITION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeADC_DATA_ACQ" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="ADC_SOURCE" target="DATA_ACQUISITION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSENSOR_DATA_ACQ" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="SENSOR_SOURCE" target="DATA_ACQUISITION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeCAN_DATA_ACQ" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="CAN_SOURCE" target="DATA_ACQUISITION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C_DATA_ACQ" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="I2C_SOURCE" target="DATA_ACQUISITION" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- MEAS 到测量类型的连接 -->
        <mxCell id="edgeMEAS_VOLTAGE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="MEAS_CORE" target="VOLTAGE_MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_CURRENT" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="MEAS_CORE" target="CURRENT_MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_TEMP" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="MEAS_CORE" target="TEMP_MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_SOC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="MEAS_CORE" target="SOC_MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_SOH" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="MEAS_CORE" target="SOH_MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_POWER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="MEAS_CORE" target="POWER_MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- MEAS 到数据输出的连接 -->
        <mxCell id="edgeMEAS_REALTIME" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="MEAS_CORE" target="REAL_TIME_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_HISTORICAL" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="MEAS_CORE" target="HISTORICAL_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_ALARM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" parent="1" source="MEAS_CORE" target="ALARM_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMEAS_STATISTICS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="MEAS_CORE" target="STATISTICS_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 标签说明 -->
        <mxCell id="LABEL_MEAS" value="MEAS 测量管理 统一数据处理" style="text;html=1;strokeColor=none;fillColor=#f3e5f5;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="800" y="350" width="200" height="50" as="geometry" />
        </mxCell>
        
        <!-- MEAS 功能说明 -->
        <mxCell id="LABEL_MEAS_FUNCTIONS" value="MEAS 功能: 多源数据采集 实时数据处理 数据校准补偿 异常检测报警 历史数据管理" style="text;html=1;strokeColor=none;fillColor=#f3e5f5;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="120" as="geometry" />
        </mxCell>
        
        <!-- 数据处理算法说明 -->
        <mxCell id="LABEL_ALGORITHMS" value="处理算法: 数字滤波 移动平均 卡尔曼滤波 线性插值 温度补偿 校准算法" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="750" width="250" height="100" as="geometry" />
        </mxCell>
        
        <!-- 测量精度说明 -->
        <mxCell id="LABEL_ACCURACY" value="测量精度: 电压 ±0.1% 电流 ±0.5% 温度 ±1°C SOC ±2% SOH ±5% 功率 ±1%" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="800" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 采样频率说明 -->
        <mxCell id="LABEL_SAMPLING" value="采样频率: 电压电流 1 kHz 温度 10 Hz SOC SOH 1 Hz 功率 100 Hz 实时响应 <1ms" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="950" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- 数据存储说明 -->
        <mxCell id="LABEL_STORAGE" value="数据存储: 实时数据 RAM 缓存 历史数据 FRAM 存储 报警记录 Flash 保存 统计数据 定期更新" style="text;html=1;strokeColor=none;fillColor=#ffe0b2;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="900" width="250" height="120" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
