<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="I2C外设连接" id="i2c-peripherals-connection">
    <mxGraphModel dx="1793" dy="1076" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="I2C1" value="I2C1 i2cREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="950" y="410" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PEX1" value="PEX1 PCA9539 I2C Address 0x74" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="150" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PEX2" value="PEX2 PCA9539 I2C Address 0x75" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="280" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PEX3" value="PEX3 PCA9539 I2C Address 0x76" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="410" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="RTC" value="RTC PCF2131 Real-Time Clock" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="540" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MUX_I2C" value="MUX ADG728 729 I2C Multiplexer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="670" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="HTSENSOR" value="HTSensor SHT30 I2C Address 0x44" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="800" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIO" value="PEX1 GPIO 16 GPIO Pins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="100" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_CONTACTOR_FB" value="Contactor Feedback" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="180" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PEX2_GPIO" value="PEX2 GPIO 16 GPIO Pins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="280" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PEX3_GPIO" value="PEX3 GPIO 16 GPIO Pins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="410" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PEX3_HTSENSOR_EN" value="HTSensor Enable Pin0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="490" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TEMP_HUMIDITY_DATA" value="Temperature Humidity Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="800" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_I2C1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" parent="1" source="MCU" target="I2C1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_PEX1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="I2C1" target="PEX1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_PEX2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="I2C1" target="PEX2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_PEX3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="I2C1" target="PEX3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_RTC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="I2C1" target="RTC" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_MUX" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="I2C1" target="MUX_I2C" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_HTSENSOR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="I2C1" target="HTSENSOR" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX1_GPIO" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PEX1" target="PEX1_GPIO" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX1_CONTACTOR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="PEX1" target="PEX1_CONTACTOR_FB" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX2_GPIO" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PEX2" target="PEX2_GPIO" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX3_GPIO" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PEX3" target="PEX3_GPIO" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX3_HTSENSOR_EN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="PEX3" target="PEX3_HTSENSOR_EN" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHTSENSOR_DATA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="HTSENSOR" target="TEMP_HUMIDITY_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_I2C_BUS" value="I2C 总线 400 kHz" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="350" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_PEX_FUNCTION" value="PEX 功能: GPIO 扩展 接触器反馈 状态指示 外设使能" style="text;html=1;strokeColor=none;fillColor=#c8e6c9;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_RTC_FUNCTION" value="RTC 功能: 实时时钟 时间戳 定时器 日历功能" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="720" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_HTSENSOR_FUNCTION" value="HTSensor 功能: 温度监测 湿度监测 环境监控 数据记录" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="840" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_I2C_ADDRESSES" value="I2C 地址: PEX1 0x74 PEX2 0x75 PEX3 0x76 HTSensor 0x44" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="960" width="250" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
