<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="foxBMS外设连接总览" id="foxbms-peripherals">
    <mxGraphModel dx="4620" dy="4737" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="m8y7Y1y-qserCYeprVfc-301" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="-1910" y="1400" width="1250" height="1320" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="MCU" target="CAN_BUS">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="490" y="400" />
              <mxPoint x="490" y="160" />
              <mxPoint x="30" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="MCU" target="SPS">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="MCU" target="m8y7Y1y-qserCYeprVfc-214">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2250" y="460" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="MCU" target="INTERLOCK_NEW">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MCU" value="MCU (NXP S32K)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="600" y="400" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="SPI1" value="SPI1 (MC33775A Tx)" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="530" y="-130" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SPI2" value="SPI2 (SPS, SBC)" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="630" y="-130" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SPI3" value="SPI3 (FRAM)" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="730" y="-130" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SPI4" value="SPI4 (MC33775A Rx)" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="830" y="-130" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="CAN_BUS" value="CAN总线" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="-20" y="-140" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="MCU" target="SPI1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="MCU" target="SPI2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="MCU" target="SPI3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI4" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;" parent="1" source="SPI_COMM" target="SPI4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="700" y="160" />
              <mxPoint x="870" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="I2C1" value="I2C1 (i2cREG1)" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="95" y="415" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="I2C_COMM" target="I2C1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PEX1" value="PEX1 (PCA9539)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="-10" y="900" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="PEX2" value="PEX2 (PCA9539)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="90" y="900" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="PEX3" value="PEX3 (PCA9539)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="190" y="900" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="RTC_I2C" value="RTC (PCF2131)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="90" y="190" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="MUX_I2C" value="MUX (ADG728/729)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="190" y="190" width="110" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_PEX1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="I2C1" target="PEX1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_PEX2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="I2C1" target="PEX2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_PEX3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="I2C1" target="PEX3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_RTC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="I2C1" target="RTC_I2C" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeI2C1_MUX" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="I2C1" target="MUX_I2C" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="SPS" value="SPS (Smart Power Switch)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="970" y="500" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="CONTACTOR_FEEDBACK" value="Contactor Feedback\n(PEX GPIO)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=11;" parent="1" vertex="1">
          <mxGeometry x="300" y="580" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX_CONTACTOR_FEEDBACK" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1" target="CONTACTOR_FEEDBACK" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="35" y="600" />
              <mxPoint x="350" y="600" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeCONTACTOR_FEEDBACK_SPS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;dashed=1;" parent="1" source="CONTACTOR_FEEDBACK" target="SPS" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="350" y="570" />
              <mxPoint x="970" y="570" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="LABEL_GPIO_FEEDBACK" value="GPIO Feedback\n(状态反馈)" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=10;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="500" y="550" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="SBC_NEW" value="SBC (系统基础芯片)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=13;" parent="1" vertex="1">
          <mxGeometry x="2600" y="420" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_SBC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;" parent="1" source="MCU" target="SBC_NEW" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="HTSENSOR" value="Sensor (传感器)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=13;" parent="1" vertex="1">
          <mxGeometry x="1180" y="500" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_HTSENSOR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;" parent="1" source="MCU" target="HTSENSOR" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="MEAS" value="MEAS (测量管理)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=13;" parent="1" vertex="1">
          <mxGeometry x="1520" y="260" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_MEAS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;dashed=1;strokeColor=#7b1fa2;" parent="1" source="MCU" target="MEAS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="INTERLOCK_NEW" value="Interlock (互锁)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=13;" parent="1" vertex="1">
          <mxGeometry x="1720" y="255" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="DMA" value="DMA (Direct Memory Access)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=13;" parent="1" vertex="1">
          <mxGeometry x="1300" y="260" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_DMA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;dashed=1;strokeColor=#7b1fa2;" parent="1" source="MCU" target="DMA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PWM" value="PWM (脉宽调制)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=13;" parent="1" vertex="1">
          <mxGeometry x="810" y="500" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_PWM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;" parent="1" source="MCU" target="PWM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_PORT0" value="PEX1 Port0" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="-10" y="960" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_PORT1" value="PEX1 Port1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="-10" y="1000" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX1_PEX1_PORT0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1" target="PEX1_PORT0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX1_PEX1_PORT1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1" target="PEX1_PORT1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PEX2_PORT0" value="PEX2 Port0" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="90" y="960" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PEX2_PORT1" value="PEX2 Port1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="90" y="1000" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX2_PEX2_PORT0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX2" target="PEX2_PORT0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX2_PEX2_PORT1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX2" target="PEX2_PORT1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PEX3_PORT0" value="PEX3 Port0" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="190" y="960" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="PEX3_PORT1" value="PEX3 Port1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f8bbd0;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="190" y="1000" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX3_PEX3_PORT0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX3" target="PEX3_PORT0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX3_PEX3_PORT1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX3" target="PEX3_PORT1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIO0" value="GPIO0 (Pin0)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;fontSize=11;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="-100" y="1210" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIO1" value="GPIO1 (Pin1)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;fontSize=11;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="-30" y="1210" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIO7" value="GPIO7 (Pin7)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;fontSize=11;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="110" y="1210" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIO8" value="GPIO8 (Pin8)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;fontSize=11;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="180" y="1210" width="60" height="25" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIODOTS" value="..." style="text;html=1;strokeColor=none;fillColor=none;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="40" y="1210" width="30" height="25" as="geometry" />
        </mxCell>
        <mxCell id="PEX1_GPIO15" value="GPIO15 (Pin15)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;fontSize=11;strokeColor=#388e3c;" parent="1" vertex="1">
          <mxGeometry x="250" y="1210" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX1_PORT0_GPIO0" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1_PORT0" target="PEX1_GPIO0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="25" y="1050" />
              <mxPoint x="-70" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgePEX1_PORT0_GPIO1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1_PORT0" target="PEX1_GPIO1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePEX1_PORT0_GPIO7" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1_PORT0" target="PEX1_GPIO7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="25" y="1050" />
              <mxPoint x="140" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgePEX1_PORT1_GPIO8" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1_PORT1" target="PEX1_GPIO8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="25" y="1050" />
              <mxPoint x="210" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgePEX1_PORT1_GPIO15" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="PEX1_PORT1" target="PEX1_GPIO15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="25" y="1050" />
              <mxPoint x="285" y="1050" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgePEX1_PORT1_GPIODOTS" style="edgeStyle=orthogonalEdgeStyle;endArrow=none;html=1;dashed=1;strokeColor=#388e3c;" parent="1" source="PEX1_PORT1" target="PEX1_GPIODOTS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="I2C_COMM" value="I2C" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="390" y="415" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="SPI_COMM" value="SPI" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="660" y="255" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="MHaxZRHWrLjhLRVIo_EP-1" value="" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;" parent="1" source="MCU" target="I2C_COMM" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="600" y="440" as="sourcePoint" />
            <mxPoint x="175" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeDMA_SPI" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;dashed=1;strokeColor=#7b1fa2;" parent="1" source="DMA" target="SPI_COMM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeDMA_I2C" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;dashed=1;strokeColor=#7b1fa2;" parent="1" source="DMA" target="I2C_COMM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_FRAM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;" parent="1" target="SPI_COMM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_SPS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;" parent="1" target="SPI_COMM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI_AFE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;" parent="1" target="SPI_COMM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-148" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="-595" y="-1580" width="1290" height="900" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-236" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="-90" y="-80" width="1380" height="980" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-110" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="1000" y="300" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-111" value="CAN1 canREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="750" y="150" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-112" value="CAN2 canREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="760" y="375" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-113" value="HET2 hetREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="430" y="550" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-114" value="PEX PCA9539" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="610" y="640" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-115" value="CAN1 Transceiver TJA1050 SN65HVD230" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="400" y="50" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-116" value="CAN2 Transceiver TJA1050 SN65HVD230" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="400" y="250" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-117" value="CAN1 Enable HET2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="10" y="525" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-118" value="CAN1 Standby HET2&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="10" y="595" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-119" value="CAN2 Enable PEX Port0&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="10" y="725" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-120" value="CAN2 Standby PEX Port0&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="10" y="795" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-121" value="IMD Device Bender ISO165C" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-122" value="Current Sensor CAN Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry y="100" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-123" value="External BMS Master Slave" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry y="200" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-124" value="Vehicle ECU Engine Control" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry y="300" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-125" value="Charger DC AC Charger" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1bee7;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry y="400" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-126" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-110" target="m8y7Y1y-qserCYeprVfc-111">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="350" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-127" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-110" target="m8y7Y1y-qserCYeprVfc-112">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-128" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-110" target="m8y7Y1y-qserCYeprVfc-113">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1060" y="510" />
              <mxPoint x="505" y="510" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-129" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-110" target="m8y7Y1y-qserCYeprVfc-114">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1125" y="580" />
              <mxPoint x="825" y="580" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-130" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-111" target="m8y7Y1y-qserCYeprVfc-115">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-131" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=3;entryX=0.815;entryY=0.985;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-112" target="m8y7Y1y-qserCYeprVfc-116">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-132" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-113" target="m8y7Y1y-qserCYeprVfc-117">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-133" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-113" target="m8y7Y1y-qserCYeprVfc-118">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-134" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-114" target="m8y7Y1y-qserCYeprVfc-119">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="685" y="750" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-135" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-114" target="m8y7Y1y-qserCYeprVfc-120">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="685" y="820" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-136" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-115" target="m8y7Y1y-qserCYeprVfc-121">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-137" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-115" target="m8y7Y1y-qserCYeprVfc-122">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-138" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-115" target="m8y7Y1y-qserCYeprVfc-123">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="525" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-139" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-116" target="m8y7Y1y-qserCYeprVfc-124">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-140" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-148" source="m8y7Y1y-qserCYeprVfc-116" target="m8y7Y1y-qserCYeprVfc-125">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-141" value="CAN1 500 kbps 主要通信总线" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="550" y="150" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-142" value="CAN2 500 kbps 隔离通信总线" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="530" y="425" width="200" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-237" value="CAN" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-148">
          <mxGeometry x="-90" y="-80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-149" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="CAN_BUS">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="-560" as="sourcePoint" />
            <mxPoint x="30" y="-760" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-214" value="AFE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#eeeeee;strokeColor=#616161;fontSize=13;" vertex="1" parent="1">
          <mxGeometry x="2290" y="260" width="140" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-218" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="2080" y="-1230" width="1690" height="800" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-184" value="" style="group" vertex="1" connectable="0" parent="m8y7Y1y-qserCYeprVfc-218">
          <mxGeometry x="20" y="90" width="1670" height="710" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-215" value="" style="rounded=0;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="-20" y="-90" width="1690" height="800" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-185" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="1180" y="250" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-186" value="SPI1 spiREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="930" y="100" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-187" value="SPI4 spiREG4" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="930" y="260" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-188" value="HET1 hetREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="940" y="520" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-189" value="AFE LTC6813-1 Linear Technology" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="580" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-190" value="AFE MC33775A NXP Tx Channel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="580" y="100" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-191" value="AFE MC33775A NXP Rx Channel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="580" y="260" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-192" value="AFE MAX17841B Maxim Integrated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="580" y="390" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-193" value="Maxim GPIO HET1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="580" y="530" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-194" value="Battery Module 1 Cell Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="180" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-195" value="Battery Module 2 Cell Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="180" y="100" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-196" value="Battery Module 3 Cell Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="180" y="200" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-197" value="Temperature Sensors NTC PTC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry y="350" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-198" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-185" target="m8y7Y1y-qserCYeprVfc-186">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1305" y="240" />
              <mxPoint x="1005" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-199" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-185" target="m8y7Y1y-qserCYeprVfc-187">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-200" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-185" target="m8y7Y1y-qserCYeprVfc-188">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1305" y="390" />
              <mxPoint x="1015" y="390" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-201" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-186" target="m8y7Y1y-qserCYeprVfc-189">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1005" y="50" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-202" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-186" target="m8y7Y1y-qserCYeprVfc-190">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-203" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-187" target="m8y7Y1y-qserCYeprVfc-191">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-204" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-187" target="m8y7Y1y-qserCYeprVfc-192">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1005" y="360" />
              <mxPoint x="705" y="360" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-205" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-188" target="m8y7Y1y-qserCYeprVfc-193">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-206" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-189" target="m8y7Y1y-qserCYeprVfc-194">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-207" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-190" target="m8y7Y1y-qserCYeprVfc-195">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-208" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-192" target="m8y7Y1y-qserCYeprVfc-196">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-209" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-184" source="m8y7Y1y-qserCYeprVfc-191" target="m8y7Y1y-qserCYeprVfc-197">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="100" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-210" value="Tx Channel 发送通道" style="text;html=1;strokeColor=none;fillColor=#fff9c4;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="830" y="90" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-211" value="Rx Channel 接收通道" style="text;html=1;strokeColor=none;fillColor=#fff9c4;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="830" y="320" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-213" value="芯片选择:&amp;nbsp;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;LTC6813-1 Linear Tech MC33775A NXP 双通道 MAX17841B Maxim&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-184">
          <mxGeometry x="1180" y="600" width="350" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-217" value="AFE" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-218">
          <mxGeometry width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-225" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1380" y="820" width="1520" height="890" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-219" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry width="1520" height="890" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-35" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="1260" y="390" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-36" value="ADC1 adcREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=16;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="1000" y="400" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-37" value="Temperature Sensor 1 NTC Thermistor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="40" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-38" value="Temperature Sensor 2 PT1000 RTD" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="170" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-39" value="Voltage Sensor 1 Pack Voltage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="280" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-40" value="Voltage Sensor 2 HV+ Voltage" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="440" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-41" value="Current Sensor 1 Hall Effect" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="550" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-42" value="Current Sensor 2 Shunt Resistor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="660" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-43" value="Pressure Sensor Coolant Pressure" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="330" y="770" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-44" value="ADC Channel 0 0-5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="720" y="50" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-45" value="ADC Channel 1 0-5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="720" y="180" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-46" value="ADC Channel 2 0-5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="720" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-47" value="ADC Channel 3 0-5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="720" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-48" value="ADC Channel 4 0-5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="720" y="450" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-49" value="ADC Channel 5 0-5V" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="1015" y="670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-50" value="Signal Conditioning 信号调理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="20" y="440" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-51" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-35" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-52" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-37" target="m8y7Y1y-qserCYeprVfc-44">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="60" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-53" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-38" target="m8y7Y1y-qserCYeprVfc-45">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-54" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-39" target="m8y7Y1y-qserCYeprVfc-46">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-55" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-40" target="m8y7Y1y-qserCYeprVfc-47">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-56" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-41" target="m8y7Y1y-qserCYeprVfc-48">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-57" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-42" target="m8y7Y1y-qserCYeprVfc-49">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-58" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-44" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-59" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-45" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-60" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-46" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-61" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-47" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-62" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-48" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-63" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-49" target="m8y7Y1y-qserCYeprVfc-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-64" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-50" target="m8y7Y1y-qserCYeprVfc-37">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="270" y="480" />
              <mxPoint x="270" y="80" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-65" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-50" target="m8y7Y1y-qserCYeprVfc-39">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="300" y="480" />
              <mxPoint x="300" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-66" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-225" source="m8y7Y1y-qserCYeprVfc-50" target="m8y7Y1y-qserCYeprVfc-41">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="300" y="480" />
              <mxPoint x="300" y="590" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-67" value="ADC1 接口 12-bit 1 MSPS" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry x="950" y="320" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-221" value="Sensor" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-225">
          <mxGeometry width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-232" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="2960" y="10" width="1880" height="960" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-226" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry width="1880" height="960" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-108" value="" style="group" vertex="1" connectable="0" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry x="240" y="50" width="1510" height="850" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-74" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="1000" y="250" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-75" value="SPI2 spiREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=16;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="770" y="260" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-76" value="SBC System Basis Chip FS6500" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=16;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="400" y="250" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-77" value="Power Supply 电源管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="-40" y="50" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-80" value="LIN Transceiver LIN 收发器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="90" y="420" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-81" value="Voltage Regulator 电压调节器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="-40" y="530" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-82" value="VDD 5V MCU 供电" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="300" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-83" value="VDD 3.3V 外设供电" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="500" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-84" value="VBAT 电池电压" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="700" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-85" value="Reset Signal 复位信号" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="300" y="600" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-86" value="Enable Signal 使能信号" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="520" y="600" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-87" value="Fault Signal 故障信号" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="700" y="600" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-88" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-74" target="m8y7Y1y-qserCYeprVfc-75">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-89" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-75" target="m8y7Y1y-qserCYeprVfc-76">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-90" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-77">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-93" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-80">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="190" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-94" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-81">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-95" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-82">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-96" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-83">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-97" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-84">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="775" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-98" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-85">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-99" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-86">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="525" y="475" />
              <mxPoint x="610" y="475" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-100" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-87">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="775" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-101" value="SPI2 接口 配置状态" style="text;html=1;strokeColor=none;fillColor=#fffde7;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="600" y="170" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-102" value="SBC 主要功能: 电源管理 系统监控 通信接口 故障保护 低功耗模式" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=12;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="1200" y="490" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-103" value="电源管理: 输入电压 6V-40V 5V 输出 150mA 3.3V 输出 100mA 效率 &gt;85% 过压欠压保护" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="1200" y="640" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-104" value="看门狗功能: 窗口看门狗 超时检测 自动复位 故障计数 安全状态切换" style="text;html=1;strokeColor=none;fillColor=#ffebee;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="1200" y="790" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-78" value="Watchdog 看门狗" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-108">
          <mxGeometry x="150" y="120" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-91" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-78">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="250" y="270" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-228" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.72;entryY=0.9;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="m8y7Y1y-qserCYeprVfc-108" source="m8y7Y1y-qserCYeprVfc-104" target="m8y7Y1y-qserCYeprVfc-104">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-79" value="CAN Transceiver CAN 收发器" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry x="20" y="310" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-92" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-232" source="m8y7Y1y-qserCYeprVfc-76" target="m8y7Y1y-qserCYeprVfc-79">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="765" y="280" />
              <mxPoint x="120" y="280" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-227" value="SBC" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-229" value="通信接口: CAN 2.0B LIN 2.1 SPI 配置 故障诊断 唤醒功能" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry x="1210" y="500" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-230" value="安全特性: ASIL-B 认证 故障安全模式 温度监控 电压监控 失效模式管理" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry x="1210" y="650" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-231" value="应用场景: 汽车电子 工业控制 电池管理 安全系统 通信网关" style="text;html=1;strokeColor=none;fillColor=#e3f2fd;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-232">
          <mxGeometry x="1210" y="800" width="200" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-235" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="-120" y="1470" width="1530" height="940" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-233" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry width="1530" height="940" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-6" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="1215" y="190" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-7" value="SPI2 spiREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="965" y="90" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-8" value="HET2 hetREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="1370" y="620" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-9" value="SPS Smart Power Switch" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=16;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="615" y="190" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-10" value="SPS Reset HET2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="1090" y="770" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-11" value="SPS Feedback HET2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="920" y="850" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-12" value="SPS CS HET2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="1275" y="850" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-13" value="Contactor Plus SPS&amp;nbsp;&lt;div&gt;Channel 0&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="215" y="90" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-14" value="Contactor Minus SPS&amp;nbsp;&lt;div&gt;Channel 1&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="10" y="200" width="180" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-15" value="Contactor Precharge SPS Channel 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="215" y="315" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-16" value="I2C1 i2cREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="965" y="490" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-17" value="PEX1 PCA9539" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="615" y="640" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-18" value="Contactor Feedback GPIO Status" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="215" y="640" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-19" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-6" target="m8y7Y1y-qserCYeprVfc-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-20" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-6" target="m8y7Y1y-qserCYeprVfc-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-21" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-6" target="m8y7Y1y-qserCYeprVfc-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-22" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-7" target="m8y7Y1y-qserCYeprVfc-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-23" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-8" target="m8y7Y1y-qserCYeprVfc-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-24" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-8" target="m8y7Y1y-qserCYeprVfc-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-25" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-8" target="m8y7Y1y-qserCYeprVfc-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-26" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-9" target="m8y7Y1y-qserCYeprVfc-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-27" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-9" target="m8y7Y1y-qserCYeprVfc-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-28" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-9" target="m8y7Y1y-qserCYeprVfc-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-29" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-16" target="m8y7Y1y-qserCYeprVfc-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-30" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-17" target="m8y7Y1y-qserCYeprVfc-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-31" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;dashed=1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-235" source="m8y7Y1y-qserCYeprVfc-18" target="m8y7Y1y-qserCYeprVfc-9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="315" y="500" />
              <mxPoint x="740" y="500" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-32" value="HET2: GPIO控制信号" style="text;html=1;strokeColor=none;fillColor=#fce4ec;fontSize=12;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="1275" y="560" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-33" value="I2C1: 反馈状态读取" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=12;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry x="790" y="550" width="150" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-234" value="SPS/Contactor" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-235">
          <mxGeometry width="220" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-241" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="-1690" y="-30" width="1400" height="1030" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-239" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-241">
          <mxGeometry width="1400" height="1030" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-150" value="" style="group" vertex="1" connectable="0" parent="m8y7Y1y-qserCYeprVfc-241">
          <mxGeometry x="120" y="110" width="1250" height="890" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-151" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="1000" y="290" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-152" value="I2C1 i2cREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=16;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="750" y="300" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-153" value="PEX1 PCA9539 I2C Address 0x74" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="400" y="40" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-154" value="PEX2 PCA9539 I2C Address 0x75" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="400" y="170" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-155" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-156" target="m8y7Y1y-qserCYeprVfc-152">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-156" value="PEX3 PCA9539 I2C Address 0x76" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="400" y="300" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-157" value="RTC PCF2131 Real-Time Clock" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="400" y="430" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-158" value="MUX ADG728 729 I2C Multiplexer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="400" y="560" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-159" value="HTSensor SHT30 I2C Address 0x44" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="400" y="690" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-160" value="PEX1 GPIO 16 GPIO Pins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-161" value="Contactor Feedback" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry y="90" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-162" value="PEX2 GPIO 16 GPIO Pins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry y="180" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-163" value="PEX3 GPIO 16 GPIO Pins" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#c8e6c9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry y="270" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-164" value="HTSensor Enable" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry y="360" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-165" value="Temperature Humidity Data" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry y="700" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-166" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-151" target="m8y7Y1y-qserCYeprVfc-152">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-167" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-152" target="m8y7Y1y-qserCYeprVfc-153">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="80" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-168" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-152" target="m8y7Y1y-qserCYeprVfc-154">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="210" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-169" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-152" target="m8y7Y1y-qserCYeprVfc-156">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-170" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-152" target="m8y7Y1y-qserCYeprVfc-157">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="470" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-171" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-152" target="m8y7Y1y-qserCYeprVfc-158">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="600" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-172" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-152" target="m8y7Y1y-qserCYeprVfc-159">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="825" y="730" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-173" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-153" target="m8y7Y1y-qserCYeprVfc-160">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-174" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-153" target="m8y7Y1y-qserCYeprVfc-161">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-175" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-154" target="m8y7Y1y-qserCYeprVfc-162">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-176" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-156" target="m8y7Y1y-qserCYeprVfc-163">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-177" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-156" target="m8y7Y1y-qserCYeprVfc-164">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-178" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-150" source="m8y7Y1y-qserCYeprVfc-159" target="m8y7Y1y-qserCYeprVfc-165">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-179" value="I2C 总线 400 kHz" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=14;fontStyle=2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="660" y="250" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-180" value="PEX 功能: GPIO 扩展 接触器反馈 状态指示 外设使能" style="text;html=1;strokeColor=none;fillColor=#c8e6c9;fontSize=12;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="880" y="450" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-181" value="RTC 功能: 实时时钟 时间戳 定时器 日历功能" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=12;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="880" y="570" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-182" value="HTSensor 功能: 温度监测 湿度监测 环境监控 数据记录" style="text;html=1;strokeColor=none;fillColor=#e1f5fe;fontSize=12;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="880" y="690" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-183" value="I2C 地址: PEX1 0x74 PEX2 0x75 PEX3 0x76 HTSensor 0x44" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-150">
          <mxGeometry x="880" y="810" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-240" value="I2C/PEX" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-241">
          <mxGeometry width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-261" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="3490" y="1740" width="1320" height="830" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-259" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry width="1320" height="830" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-242" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="1110" y="320" width="180" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-243" value="SPI3 spiREG3" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=16;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="860" y="330" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-244" value="FRAM Ferroelectric RAM FM25V20A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=16;fontStyle=1;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="510" y="320" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-245" value="Configuration Data 配置参数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="90" y="100" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-246" value="Calibration Data 校准数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="290" y="190" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-247" value="SOC Data 电量状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="320" y="470" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-248" value="SOH Data 健康状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="180" y="590" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-249" value="Error Log 错误日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=14;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="40" y="710" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-250" value="FRAM 特性: 非易失性存储 快速读写 70ns 无限次擦写 低功耗 高可靠性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="650" y="500" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-251" value="SPI3 配置: 时钟频率 10 MHz 模式 Mode 0 数据位 8-bit 字节序 MSB First" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=12;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry x="650" y="650" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-252" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-242" target="m8y7Y1y-qserCYeprVfc-243">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-253" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-243" target="m8y7Y1y-qserCYeprVfc-244">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-254" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-244" target="m8y7Y1y-qserCYeprVfc-245">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-255" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-244" target="m8y7Y1y-qserCYeprVfc-246">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-256" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-244" target="m8y7Y1y-qserCYeprVfc-247">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-257" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-244" target="m8y7Y1y-qserCYeprVfc-248">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-258" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="m8y7Y1y-qserCYeprVfc-261" source="m8y7Y1y-qserCYeprVfc-244" target="m8y7Y1y-qserCYeprVfc-249">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="150" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-260" value="FRAM" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="m8y7Y1y-qserCYeprVfc-261">
          <mxGeometry width="130" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-262" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" vertex="1" parent="1">
          <mxGeometry x="-880" y="2180" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-263" value="GIOA gioREG" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="-990" y="1610" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-264" value="GIOB gioREG" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="-1000" y="1820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-265" value="HET1 hetREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="-1000" y="1970" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-266" value="HET2 hetREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="-1400" y="2200" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-267" value="PWM etpwmREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="-1040" y="2500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-268" value="Status LED GIOA&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1490" y="1750" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-269" value="Error LED GIOA" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1290" y="1750" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-270" value="Main Relay GIOB&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1500" y="1900" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-271" value="Precharge Relay GIOB" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1300" y="1900" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-272" value="Interlock Output HET1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1580" y="2050" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-273" value="Maxim Enable HET1&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1380" y="2050" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-274" value="SPS Control HET2&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1860" y="2200" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-275" value="CAN Control HET2&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1610" y="2290" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-276" value="Interlock Input HET1&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1860" y="2100" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-277" value="Emergency Stop GIOA&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1790" y="1750" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-278" value="Cooling Fan PWM Channel 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1480" y="2430" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-279" value="Buzzer PWM Channel 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1280" y="2430" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-280" value="Battery Heater PWM Channel 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="-1600" y="2610" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-281" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-262" target="m8y7Y1y-qserCYeprVfc-263">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-795" y="1640" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-282" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-262" target="m8y7Y1y-qserCYeprVfc-264">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-795" y="1850" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-283" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-262" target="m8y7Y1y-qserCYeprVfc-265">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-795" y="2000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-284" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-262" target="m8y7Y1y-qserCYeprVfc-266">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-285" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-262" target="m8y7Y1y-qserCYeprVfc-267">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-795" y="2530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-286" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-263" target="m8y7Y1y-qserCYeprVfc-268">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-287" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-263" target="m8y7Y1y-qserCYeprVfc-269">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-288" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-264" target="m8y7Y1y-qserCYeprVfc-270">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-289" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-264" target="m8y7Y1y-qserCYeprVfc-271">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-290" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-265" target="m8y7Y1y-qserCYeprVfc-272">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-291" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-265" target="m8y7Y1y-qserCYeprVfc-273">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-292" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-266" target="m8y7Y1y-qserCYeprVfc-274">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-293" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-266" target="m8y7Y1y-qserCYeprVfc-275">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-294" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-276" target="m8y7Y1y-qserCYeprVfc-265">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-295" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-277" target="m8y7Y1y-qserCYeprVfc-263">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-1700" y="1540" />
              <mxPoint x="-930" y="1540" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-296" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-267" target="m8y7Y1y-qserCYeprVfc-278">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-297" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-267" target="m8y7Y1y-qserCYeprVfc-279">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-298" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" edge="1" parent="1" source="m8y7Y1y-qserCYeprVfc-267" target="m8y7Y1y-qserCYeprVfc-280">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-299" value="GPIO 接口 数字输入输出" style="text;html=1;strokeColor=none;fillColor=#fce4ec;fontSize=14;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="-1005" y="1720" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-300" value="PWM 接口 脉宽调制" style="text;html=1;strokeColor=none;fillColor=#f3e5f5;fontSize=14;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="-1190" y="2560" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="m8y7Y1y-qserCYeprVfc-302" value="GIO/HET/PWM" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FF0000;fontStyle=1;fontSize=30;" vertex="1" parent="1">
          <mxGeometry x="-1910" y="1400" width="210" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
