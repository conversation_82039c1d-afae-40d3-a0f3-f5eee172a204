<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="AFE模拟前端连接" id="afe-connection">
    <mxGraphModel dx="1793" dy="1076" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="SPI1" value="SPI1 spiREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="250" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="SPI4" value="SPI4 spiREG4" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="950" y="450" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="HET1" value="HET1 hetREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="960" y="670" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AFE_LTC" value="AFE LTC6813-1 Linear Technology" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="150" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AFE_NXP_TX" value="AFE MC33775A NXP Tx Channel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="280" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AFE_NXP_RX" value="AFE MC33775A NXP Rx Channel" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="410" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="AFE_MAXIM" value="AFE MAX17841B Maxim Integrated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="540" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="MAXIM_GPIO" value="Maxim GPIO HET1 Pin21" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="680" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="BATTERY_MODULE1" value="Battery Module 1 Cell Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="150" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="BATTERY_MODULE2" value="Battery Module 2 Cell Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="280" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="BATTERY_MODULE3" value="Battery Module 3 Cell Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="410" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="TEMP_SENSORS" value="Temperature Sensors NTC PTC" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="200" y="540" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_SPI1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" parent="1" source="MCU" target="SPI1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_SPI4" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" parent="1" source="MCU" target="SPI4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_HET1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="HET1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1325" y="540" />
              <mxPoint x="1035" y="540" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeSPI1_LTC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=3;" parent="1" source="SPI1" target="AFE_LTC" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI1_NXP_TX" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" parent="1" source="SPI1" target="AFE_NXP_TX" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI4_NXP_RX" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=3;" parent="1" source="SPI4" target="AFE_NXP_RX" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI4_MAXIM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="SPI4" target="AFE_MAXIM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET1_MAXIM_GPIO" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=2;" parent="1" source="HET1" target="MAXIM_GPIO" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeAFE_LTC_MODULE1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="AFE_LTC" target="BATTERY_MODULE1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeAFE_NXP_MODULE2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="AFE_NXP_TX" target="BATTERY_MODULE2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeAFE_MAXIM_MODULE3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="AFE_MAXIM" target="BATTERY_MODULE3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeAFE_TEMP" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="AFE_NXP_RX" target="TEMP_SENSORS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_TX" value="Tx Channel 发送通道" style="text;html=1;strokeColor=none;fillColor=#fff9c4;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="150" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_RX" value="Rx Channel 接收通道" style="text;html=1;strokeColor=none;fillColor=#fff9c4;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="550" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_FUNCTION" value="AFE 功能: 电池电压监测 温度监测 平衡控制 故障检测" style="text;html=1;strokeColor=none;fillColor=#e3f2fd;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="80" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CHIP_SELECT" value="芯片选择: LTC6813-1 Linear Tech MC33775A NXP 双通道 MAX17841B Maxim" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="750" width="250" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
