<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="FRAM存储器连接" id="fram-storage-connection">
    <mxGraphModel dx="1793" dy="1076" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MCU" value="MCU TI TMS570LC4357" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="SPI3" value="SPI3 spiREG3" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="950" y="410" width="150" height="80" as="geometry" />
        </mxCell>
        <mxCell id="FRAM" value="FRAM Ferroelectric RAM FM25V20A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="600" y="400" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="CONFIG_DATA" value="Configuration Data 配置参数" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="200" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CALIBRATION_DATA" value="Calibration Data 校准数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="320" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="SOC_DATA" value="SOC Data 电量状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="440" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="SOH_DATA" value="SOH Data 健康状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="560" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="ERROR_LOG" value="Error Log 错误日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="200" y="680" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="FRAM_FEATURES" value="FRAM 特性: 非易失性存储 快速读写 70ns 无限次擦写 低功耗 高可靠性" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="550" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="SPI_CONFIG" value="SPI3 配置: 时钟频率 10 MHz 模式 Mode 0 数据位 8-bit 字节序 MSB First" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fffde7;strokeColor=#bfa800;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="700" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_SPI3" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" parent="1" source="MCU" target="SPI3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeSPI3_FRAM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#bfa800;strokeWidth=3;" parent="1" source="SPI3" target="FRAM" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeFRAM_CONFIG" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="FRAM" target="CONFIG_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeFRAM_CALIBRATION" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="FRAM" target="CALIBRATION_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeFRAM_SOC" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="FRAM" target="SOC_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeFRAM_SOH" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="FRAM" target="SOH_DATA" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeFRAM_ERROR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" parent="1" source="FRAM" target="ERROR_LOG" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_SPI3" value="SPI3 接口 10 MHz" style="text;html=1;strokeColor=none;fillColor=#fffde7;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="850" y="350" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_CAPACITY" value="存储容量: 总容量 256 Kbit 可用空间 32 KB 页大小 无限制 地址范围 0x0000-0x7FFF" style="text;html=1;strokeColor=none;fillColor=#fff3e0;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="600" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_DATA_PARTITION" value="数据分区: 配置区 0x0000-0x0FFF 校准区 0x1000-0x1FFF SOC区 0x2000-0x2FFF SOH区 0x3000-0x3FFF 日志区 0x4000-0x7FFF" style="text;html=1;strokeColor=none;fillColor=#f5f5f5;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="750" width="250" height="120" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_APPLICATIONS" value="应用场景: 系统参数存储 电池状态记录 故障信息保存 校准数据备份 运行时间统计" style="text;html=1;strokeColor=none;fillColor=#e3f2fd;fontSize=12;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="1200" y="920" width="250" height="100" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_OPERATIONS" value="读写操作: 读取 FRAM_ReadData 写入 FRAM_WriteData 擦除 不需要 校验 CRC 检查 备份 自动镜像" style="text;html=1;strokeColor=none;fillColor=#fffde7;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="600" width="200" height="120" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_ADVANTAGES" value="FRAM 优势: 比 EEPROM 快 1000 倍 无写入延迟 10^15 次写入寿命 数据保持 10 年 抗辐射能力强" style="text;html=1;strokeColor=none;fillColor=#e8f5e9;fontSize=11;fontStyle=0;" parent="1" vertex="1">
          <mxGeometry x="950" y="750" width="200" height="120" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
