<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.5 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.5">
  <diagram name="GPIO和PWM连接" id="gpio-pwm-connection">
    <mxGraphModel dx="1554" dy="2276" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="1200" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="MCU" value="MCU" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;fontSize=16;fontStyle=1;strokeColor=#1976d2;" parent="1" vertex="1">
          <mxGeometry x="1200" y="480" width="170" height="100" as="geometry" />
        </mxCell>
        <mxCell id="GIOA" value="GIOA gioREG" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1090" y="-90" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GIOB" value="GIOB gioREG" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1080" y="120" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="HET1" value="HET1 hetREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1080" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="HET2" value="HET2 hetREG2" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#c2185b;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="680" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="PWM_ETPWM" value="PWM etpwmREG1" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="1040" y="800" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LED_STATUS" value="Status LED GIOA Pin0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="590" y="50" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LED_ERROR" value="Error LED GIOA Pin1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="790" y="50" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="RELAY_MAIN" value="Main Relay GIOB Pin0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="580" y="200" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="RELAY_PRECHARGE" value="Precharge Relay GIOB Pin1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="780" y="200" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="INTERLOCK_OUT" value="Interlock Output HET1 Pin29" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="500" y="350" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="MAXIM_ENABLE" value="Maxim Enable HET1 Pin21" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="700" y="350" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="SPS_CONTROL" value="SPS Control HET2 Pin16 9 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="220" y="500" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CAN_CONTROL" value="CAN Control HET2 Pin18 23" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="470" y="590" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="INTERLOCK_IN" value="Interlock Input HET1 Pin30" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#0288d1;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="220" y="400" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="EMERGENCY_STOP" value="Emergency Stop GIOA Pin7" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffebee;strokeColor=#d32f2f;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="290" y="50" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FAN_CONTROL" value="Cooling Fan PWM Channel 1" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e9;strokeColor=#388e3c;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="600" y="730" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="BUZZER_CONTROL" value="Buzzer PWM Channel 2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe0b2;strokeColor=#f57c00;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="800" y="730" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="HEATER_CONTROL" value="Battery Heater PWM Channel 3" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#7b1fa2;fontSize=12;" parent="1" vertex="1">
          <mxGeometry x="480" y="910" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_GIOA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="GIOA" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1285" y="-60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeMCU_GIOB" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="GIOB" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1285" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeMCU_HET1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="HET1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1285" y="300" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeMCU_HET2" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#c2185b;strokeWidth=3;" parent="1" source="MCU" target="HET2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeMCU_PWM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=3;" parent="1" source="MCU" target="PWM_ETPWM" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1285" y="830" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgeGIOA_LED_STATUS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="GIOA" target="LED_STATUS" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeGIOA_LED_ERROR" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" parent="1" source="GIOA" target="LED_ERROR" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeGIOB_RELAY_MAIN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="GIOB" target="RELAY_MAIN" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeGIOB_RELAY_PRECHARGE" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="GIOB" target="RELAY_PRECHARGE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET1_INTERLOCK_OUT" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="HET1" target="INTERLOCK_OUT" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET1_MAXIM" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="HET1" target="MAXIM_ENABLE" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET2_SPS" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="HET2" target="SPS_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeHET2_CAN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="HET2" target="CAN_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeINTERLOCK_IN_HET1" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#0288d1;strokeWidth=2;" parent="1" source="INTERLOCK_IN" target="HET1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgeEMERGENCY_GIOA" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#d32f2f;strokeWidth=2;" parent="1" source="EMERGENCY_STOP" target="GIOA" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="380" y="-160" />
              <mxPoint x="1150" y="-160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="edgePWM_FAN" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#388e3c;strokeWidth=2;" parent="1" source="PWM_ETPWM" target="FAN_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePWM_BUZZER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#f57c00;strokeWidth=2;" parent="1" source="PWM_ETPWM" target="BUZZER_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="edgePWM_HEATER" style="edgeStyle=orthogonalEdgeStyle;endArrow=block;html=1;strokeColor=#7b1fa2;strokeWidth=2;" parent="1" source="PWM_ETPWM" target="HEATER_CONTROL" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_GPIO" value="GPIO 接口 数字输入输出" style="text;html=1;strokeColor=none;fillColor=#fce4ec;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="1075" y="20" width="150" height="50" as="geometry" />
        </mxCell>
        <mxCell id="LABEL_PWM" value="PWM 接口 脉宽调制" style="text;html=1;strokeColor=none;fillColor=#f3e5f5;fontSize=14;fontStyle=2;" parent="1" vertex="1">
          <mxGeometry x="890" y="860" width="150" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
